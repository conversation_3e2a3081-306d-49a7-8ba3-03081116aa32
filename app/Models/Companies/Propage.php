<?php

namespace App\Models\Companies;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\HasMedia;

class Propage extends Model implements HasMedia
{
    use InteractsWithMedia;
    
    protected $guarded = ['id'];
    protected $casts = [
        'config' => 'object',
        'layout' => 'object'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
