<?php

namespace App\Http\Controllers\Company;

use Illuminate\Http\Request;

use App\Models\Companies\Company;
use App\Http\Controllers\Controller;
use App\Services\Company\PropageService;

class PropageController extends Controller
{
    public function __construct(
        protected PropageService $service,
    ) {
        $this->middleware(['auth:api','verified']);
    }

    // GET /companies/1/propage
    public function show(Company $company)
    {
        return $this->jsonResponse(
            $this->service->get($company->id)
        );
    }

    // PUT /companies/1/propage
    public function update(Request $req, Company $company)
    {
        $this->authorize('managePropage', $company);
        $data = $req->all();
        $data['config'] = json_decode($data['config'], true);
        $data['layout'] = json_decode($data['layout'], true);

        return $this->jsonResponse(
            $this->service->updateOrCreate(
                $company,
                $data
            )
        );
    }

}
