<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules;

use App\Models\Users\User;
use App\Models\Companies\Company;
use App\Services\User\UserService;
use App\Services\Company\CompanyMemberService;
use App\Services\Company\CompanyTokenService;

class RegisteredUserController extends Controller
{
    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(
        Request $request,
        UserService $userCommand
    )
    {
        $data = $request->validate([
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'password' => ['required', Rules\Password::min(8)->mixedCase()->numbers()],
            'referral' => ['nullable', 'string'],
            'profile' => ['required', 'array'],
            'profile.name' => ['required', 'string'],
            'profile.surname' => ['string'],
            'profile.middle_name' => ['string'],
            'profile.birthday' => ['date'],
            'profile.male' => ['boolean'],
            'profile.loc_1_id' => ['integer'],
            'profile.loc_2_id' => ['integer'],
            'profile.loc_3_id' => ['integer'],
            'profile.loc_1_name' => ['string'],
            'profile.loc_2_name' => ['string'],
            'profile.loc_3_name' => ['string'],
            'agreements' => ['nullable']
        ]);
        $user = $userCommand->register($data);
        if (isset($data['referral']))
        {
            $parts = explode(':', $data['referral']);
            $company = Company::find(intval($parts[0]));
            if ($company)
            {
                $token = (new CompanyTokenService())->getActive($company, $parts[1]);
                if ($token) {
                    (new CompanyMemberService())->assignByToken($token, $user->profile);
                }
            }
        }

        return response()->json(
            $userCommand->show($user)
        );
    }
}
