<?php

namespace App\Http\Controllers\Profile;

use Illuminate\Http\Request;

use App\Http\Controllers\Controller;
use App\Services\Profile\StudentQueryService;

class ProfileSearchController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth:api','verified']);
    }

    // POST /students/search
    public function students(StudentQueryService $query, Request $req)
    {
        if (!$req->user()->isAdmin()) {
            return $this->forbid();
        }
        return $this->jsonResponse($query->search($req->all()));
    }

}