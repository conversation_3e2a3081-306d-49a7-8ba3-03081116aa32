<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Relations\Relation;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Relation::enforceMorphMap([
            'banners'           => 'App\Models\Contents\Banner',
            'courses'           => 'App\Models\Offers\Course',
            'companies'         => 'App\Models\Companies\Company',
            'events'            => 'App\Models\Companies\Event',
            'employers'         => 'App\Models\Employers\Employer',
            'propages'          => 'App\Models\Companies\Propage',
            'institutions'      => 'App\Models\Institutions\Institution',
            'users'             => 'App\Models\Users\User',
            'contents'          => 'App\Models\Contents\Content',
            'news'              => 'App\Models\Companies\News',
            'partners'          => 'App\Models\Contents\Partner',
            'programs'          => 'App\Models\Institutions\Program',
            'faculties'         => 'App\Models\Institutions\Faculty',
            'specialities'      => 'App\Models\Institutions\Speciality',
            'vacancies'         => 'App\Models\Offers\Vacancy',
            'practices'         => 'App\Models\Offers\Practice',
            'target_vacancies'  => 'App\Models\Offers\TargetVacancy',
            'profiles'          => 'App\Models\Profiles\Profile',
            'groups'            => 'App\Models\Groups\Group',
            'group_posts'       => 'App\Models\Groups\GroupPost'
        ]);
    }
}
