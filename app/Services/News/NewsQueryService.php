<?php

namespace App\Services\News;

use App\Queries\NewsQuery;
use App\Models\Companies\News;

class NewsQueryService
{
    public function format(News $news)
    {
        return $news->load('company:id,title,logo_url');
    }

    public function search(array $form = [])
    {
        $query = (new NewsQuery())
            ->with('company:id,title');

        if (array_key_exists('admin', $form))
        {
            $query->fromAdmin();
        }
        else if (array_key_exists('company_id', $form))
        {
            $query->whereCompany($form['company_id']);
        }

        return $query->getPaginated(
            $form['page'] ?? 1,
            $form['per_page'] ?? 10,
            $form['paginate'] ?? false
        );
    }
}