<?php

namespace App\Services\Group;

use App\Models\Groups\Group;
use App\Queries\GroupQuery;

class GroupQueryService
{
    public function search(array $filters)
    {
        return (new GroupQuery())
            ->whereTitle($filters['title'] ?? null)
            ->whereOwnerId($filters['owner_id'] ?? null)
            ->whereMember($filters['member_id'] ?? null)
            ->wherePublic($filters['public'] ?? false)
            ->getPaginated(
                $filters['page'] ?? 1,
                $filters['per_page'] ?? 15,
                $filters['paginate'] ?? false
            );
    }

    public function institutionsOf(int $profileId)
    {
        return (new GroupQuery())
            ->whereMember($profileId)
            ->get();
    }

    public function format(Group $group)
    {
        $formatted = $group->load([
            'owner:id,name,surname,middle_name',
            'members:id,name,surname,middle_name',
            'roles:id,name,permissions,is_default,default,group_id'
        ])->loadCount('pendingRequests');

        $media = $group->getFirstMedia('image');
        if ($media) {
            $formatted->image_url = $media->getUrl('thumb');
        }
        
        return $formatted;
    }
}
