<?php

namespace App\Services\Company;

use App\Queries\InstitutionQuery;

class InstitutionQueryService
{
    public function search(array $form)
    {
        $query = (new InstitutionQuery())
            ->wherePublic()
            ->whereIds($form['ids'] ?? null)
            ->whereTitle($form['title'] ?? null)
            ->whereState($form['state'] ?? null)
            ->whereLoc($form['loc_id'] ?? null)
            ->whereType($form['type'] ?? null)
            ->whereManaged($form['managed'] ?? null)
            ->whereProgramForms($form['forms'] ?? null)
            ->whereProgramPrice($form['min_price'] ?? null, $form['max_price'] ?? null)
            ->orderByConcatenated($form['order'] ?? 'id-desc')
            ->paginate($form['page'] ?? 1, $form['per_page'] ?? 25);

        if (!empty($form['accreditation'])) {
            $query->whereAccreditation();
        }
        if (!empty($form['hostel'])) {
            $query->whereHostel();
        }
        if (!empty($form['military_chair'])) {
            $query->whereMilitaryChair();
        }
        if (!empty($form['budget'])) {
            $query->whereBudgetPrograms();
        }
        
        return $query->get();
    }

    public function show(int $id)
    {
        return (new InstitutionQuery())->find($id);
    }
    
}