<?php

namespace App\Services\Company;

use App\Models\Profiles\Profile;
use App\Models\Companies\Company;
use App\Models\Companies\CompanyToken;
use App\Models\Companies\CompanyMemberRole;

class CompanyMemberService
{
    public function getMembership(Company $company, Profile $profile)
    {
        return $company->memberships()->where('profile_id', $profile->id)->first();
    }

    public function isMember(Company $company, Profile $profile) {
        return $company->memberships()->where('profile_id', $profile->id)->exists();
    }

    public function assignOwner(Company $company, Profile $profile)
    {
        $role = (new CompanyMemberRoleService())->getOrCreateOwner($company);
        $this->assignRole($company, $profile, $role);
        $company->managed = true;
        $company->save();
    }

    public function assignRole(Company $company, Profile $profile, CompanyMemberRole $role)
    {
        if ($this->isMember($company, $profile)) {
            $company->members()->updateExistingPivot($profile->id, [
                'role_id' => $role->id
            ]);
        } else {
            $company->members()->attach($profile->id, [
                'role_id' => $role->id
            ]);
        }
    }

    public function assignRoleId(Company $company, Profile $profile, int $roleId)
    {
        $role = (new CompanyMemberRoleService())->get($company, $roleId);
        $this->assignRole($company, $profile, $role);
    }

    public function remove(Company $company, Profile $profile)
    {
        $company->members()->detach($profile->id);
    }

    public function assignByToken(CompanyToken $token, Profile $profile)
    {
        $this->assignRole($token->company, $profile, $token->role);
    }
}