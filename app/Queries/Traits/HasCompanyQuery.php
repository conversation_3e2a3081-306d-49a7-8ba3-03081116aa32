<?php

namespace App\Queries\Traits;

trait HasCompanyQuery
{

    public function whereCompany($companyId)
    {
        if (isset($companyId)) {
            if (is_array($companyId)) {
                if (count($companyId) > 1) {
                    $this->query->whereIn("{$this->tableName}.company_id", $companyId);
                }
            } else {
                $this->query->where("{$this->tableName}.company_id", $companyId);
            }
        }
        return $this;
    }

    public function whereCompanyIn($companyIds)
    {
        if (!empty($companyIds)) {
            $this->query->whereIn("{$this->tableName}.company_id", $companyIds);
        }
        return $this;
    }

}