<?php

namespace App\Queries\Traits;

use Illuminate\Support\Facades\DB;

trait IsCompanyQuery
{
    use HasLocationQuery, HasLocationPointQuery;

    protected $companyColumns = [
        'companies.id',
        'companies.title',
        'companies.logo_url',
        'companies.header_url',
        'companies.vip',
        'companies.infopartner',
        'companies.managed',
        'companies.company_type',
        'companies.loc_1_id',
        'companies.loc_2_id',
        'companies.loc_3_id',
        'companies.loc_1_name',
        'companies.loc_2_name',
        'companies.loc_3_name',
        'companies.loc_point',
        'companies.created_at'
    ];
    protected $role = null;

    public function whereManaged($managed = null)
    {
        if ($managed !== null) {
            $this->query->where('companies.managed', $managed);
        }
        return $this;
    }

    public function whereHasRole($role = null)
    {
        $role ??= $this->role;
        if ($role) {
            $this->query
                ->join(
                    'company_roles',
                    'companies.id',
                    '=',
                    'company_roles.company_id'
                )
                ->where('company_roles.role', $role);
        }
        return $this;
    }

    public function whereHasVacancies()
    {
        $this->query->has('vacancies');
        return $this;
    }

    public function whereDomains($domainIds)
    {
        if (!empty($domainIds)) {
            $this->query->whereExists(
                fn($q) => $q->select(DB::raw(1))
                    ->from('company_domain')
                    ->whereColumn('company_domain.company_id', 'companies.id')
                    ->whereIn('company_domain.company_id', $domainIds)
            );
        }
        return $this;
    }

}